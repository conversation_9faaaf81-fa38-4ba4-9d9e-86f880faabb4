import React, { useState, useEffect } from 'react';
import { ChatInterface } from '@/components/ChatInterface';
import { MemoryList } from '@/components/MemoryList';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Brain, MessageSquare, List, Settings } from 'lucide-react';
import { memoryApi } from '@/lib/api';

type View = 'chat' | 'memories';

function App() {
  const [currentView, setCurrentView] = useState<View>('chat');
  const [userId, setUserId] = useState('');
  const [isUserIdSet, setIsUserIdSet] = useState(false);
  const [memoryRefreshTrigger, setMemoryRefreshTrigger] = useState(0);
  const [serviceStatus, setServiceStatus] = useState<any>(null);

  useEffect(() => {
    // Load user ID from localStorage
    const savedUserId = localStorage.getItem('memory-agent-user-id');
    if (savedUserId) {
      setUserId(savedUserId);
      setIsUserIdSet(true);
    }

    // Check service status
    checkServiceStatus();
  }, []);

  const checkServiceStatus = async () => {
    try {
      const status = await memoryApi.getStatus();
      setServiceStatus(status.data);
    } catch (error) {
      console.error('Service not available:', error);
    }
  };

  const handleUserIdSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (userId.trim()) {
      localStorage.setItem('memory-agent-user-id', userId.trim());
      setIsUserIdSet(true);
    }
  };

  const handleMemoryAdded = () => {
    setMemoryRefreshTrigger(prev => prev + 1);
  };

  const resetUserId = () => {
    localStorage.removeItem('memory-agent-user-id');
    setUserId('');
    setIsUserIdSet(false);
    setCurrentView('chat');
  };

  if (!isUserIdSet) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-primary rounded-full flex items-center justify-center mb-4">
              <Brain className="w-6 h-6 text-primary-foreground" />
            </div>
            <CardTitle className="text-2xl">Memory Agent</CardTitle>
            <p className="text-muted-foreground">
              Enter your user ID to start managing your memories
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleUserIdSubmit} className="space-y-4">
              <Input
                type="text"
                placeholder="Enter your user ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                required
              />
              <Button type="submit" className="w-full">
                Get Started
              </Button>
            </form>
            
            {serviceStatus && (
              <div className="mt-6 p-3 bg-muted rounded-lg">
                <p className="text-xs text-muted-foreground mb-2">Service Status:</p>
                <div className="text-xs space-y-1">
                  <div className="flex justify-between">
                    <span>Status:</span>
                    <span className={serviceStatus.status === 'ok' ? 'text-green-600' : 'text-red-600'}>
                      {serviceStatus.status}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Memory Service:</span>
                    <span className={serviceStatus.memory_service_available ? 'text-green-600' : 'text-red-600'}>
                      {serviceStatus.memory_service_available ? 'Available' : 'Unavailable'}
                    </span>
                  </div>
                  {serviceStatus.models && (
                    <div className="mt-2 pt-2 border-t border-border">
                      <p className="font-medium">Models:</p>
                      <div className="ml-2">
                        <div>LLM: {serviceStatus.models.llm.model}</div>
                        <div>Embedder: {serviceStatus.models.embedder.model}</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              <Brain className="w-4 h-4 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-lg font-semibold">Memory Agent</h1>
              <p className="text-xs text-muted-foreground">User: {userId}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant={currentView === 'chat' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setCurrentView('chat')}
            >
              <MessageSquare className="w-4 h-4 mr-2" />
              Chat
            </Button>
            <Button
              variant={currentView === 'memories' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setCurrentView('memories')}
            >
              <List className="w-4 h-4 mr-2" />
              Memories
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={resetUserId}
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="h-[calc(100vh-73px)]">
        {currentView === 'chat' ? (
          <ChatInterface userId={userId} onMemoryAdded={handleMemoryAdded} />
        ) : (
          <MemoryList userId={userId} refreshTrigger={memoryRefreshTrigger} />
        )}
      </main>
    </div>
  );
}

export default App;
