import axios from 'axios';
import { MemoryInput, MemorySearchInput, MemoryResponse, MemorySearchResponse } from '@/types/memory';

const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

export const memoryApi = {
  // Get service status
  getStatus: () => api.get('/'),
  
  // Get all memories for a user
  getAllMemories: (userId: string): Promise<MemorySearchResponse> =>
    api.get(`/memories/${userId}`).then(res => res.data),
  
  // Add a new memory
  addMemory: (memoryInput: MemoryInput): Promise<MemoryResponse> =>
    api.post('/memories', memoryInput).then(res => res.data),
  
  // Search memories
  searchMemories: (searchInput: MemorySearchInput): Promise<MemorySearchResponse> =>
    api.post('/memories/search', searchInput).then(res => res.data),
  
  // Delete a memory
  deleteMemory: (memoryId: string): Promise<MemoryResponse> =>
    api.delete(`/memories/${memoryId}`).then(res => res.data),
  
  // Delete all memories for a user
  deleteAllUserMemories: (userId: string): Promise<MemoryResponse> =>
    api.delete(`/memories/user/${userId}`).then(res => res.data),
};
