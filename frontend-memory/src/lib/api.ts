import axios from 'axios';
import { MemoryInput, MemorySearchInput, MemoryResponse, MemorySearchResponse } from '@/types/memory';

const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 second timeout
});

// Add request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.data);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for logging
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url, response.data);
    return response;
  },
  (error) => {
    console.error('API Response Error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      data: error.response?.data,
      message: error.message
    });
    return Promise.reject(error);
  }
);

export const memoryApi = {
  // Get service status
  getStatus: () => api.get('/'),

  // Get all memories for a user
  getAllMemories: (userId: string): Promise<MemorySearchResponse> =>
    api.get(`/memories/${userId}`).then(res => res.data),

  // Add a new memory
  addMemory: (memoryInput: MemoryInput): Promise<MemoryResponse> =>
    api.post('/memories', memoryInput).then(res => res.data),

  // Search memories
  searchMemories: (searchInput: MemorySearchInput): Promise<MemorySearchResponse> =>
    api.post('/memories/search', searchInput).then(res => res.data),

  // Delete a memory
  deleteMemory: (memoryId: string): Promise<MemoryResponse> =>
    api.delete(`/memories/${memoryId}`).then(res => res.data),

  // Delete all memories for a user
  deleteAllUserMemories: (userId: string): Promise<MemoryResponse> =>
    api.delete(`/memories/user/${userId}`).then(res => res.data),
};
