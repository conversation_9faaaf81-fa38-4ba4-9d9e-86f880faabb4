import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Search, Trash2, RefreshCw, Brain, Calendar } from 'lucide-react';
import { memoryApi } from '@/lib/api';
import { Memory } from '@/types/memory';
import { toast } from 'sonner';

interface MemoryListProps {
  userId: string;
  refreshTrigger: number;
}

export function MemoryList({ userId, refreshTrigger }: MemoryListProps) {
  const [memories, setMemories] = useState<Memory[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  const loadMemories = async () => {
    setIsLoading(true);
    try {
      const response = await memoryApi.getAllMemories(userId);
      setMemories(response.memories || []);
    } catch (error: any) {
      console.error('Error loading memories:', error);

      if (error.code === 'ECONNABORTED') {
        toast.error('Loading memories timed out', {
          description: 'The memory service is taking too long to respond. Memories may still be processing in the background.',
        });
      } else {
        toast.error('Failed to load memories', {
          description: 'There was an error retrieving your memories. Please try again.',
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const searchMemories = async () => {
    if (!searchQuery.trim()) {
      loadMemories();
      return;
    }

    setIsSearching(true);
    try {
      const response = await memoryApi.searchMemories({
        query: searchQuery,
        user_id: userId,
        limit: 20
      });
      setMemories(response.memories || []);
    } catch (error) {
      console.error('Error searching memories:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const deleteMemory = async (memoryId: string) => {
    try {
      await memoryApi.deleteMemory(memoryId);
      setMemories(prev => prev.filter(m => m.id !== memoryId));
    } catch (error) {
      console.error('Error deleting memory:', error);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return 'Unknown date';
    }
  };

  useEffect(() => {
    loadMemories();
  }, [userId, refreshTrigger]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchMemories();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  return (
    <div className="flex flex-col h-full">
      {/* Search and Refresh */}
      <div className="p-3 border-b bg-muted/30">
        <div className="flex items-center gap-2 mb-3">
          <Button
            variant="ghost"
            size="icon"
            onClick={loadMemories}
            disabled={isLoading}
            className="h-8 w-8"
          >
            <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          <span className="text-sm text-muted-foreground">
            {memories.length} {memories.length === 1 ? 'memory' : 'memories'}
          </span>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
          <Input
            placeholder="Search memories..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9 h-8 text-sm"
          />
          {isSearching && (
            <RefreshCw className="absolute right-3 top-1/2 transform -translate-y-1/2 h-3 w-3 animate-spin text-muted-foreground" />
          )}
        </div>
      </div>

      {/* Memory List */}
      <ScrollArea className="flex-1">
        <div className="p-3 space-y-2">
          {isLoading && memories.length === 0 ? (
            <div className="text-center py-8">
              <RefreshCw className="mx-auto h-6 w-6 animate-spin text-muted-foreground mb-3" />
              <p className="text-sm text-muted-foreground">Loading memories...</p>
            </div>
          ) : memories.length === 0 ? (
            <div className="text-center py-8">
              <Brain className="mx-auto h-8 w-8 text-muted-foreground/50 mb-3" />
              <p className="text-sm text-muted-foreground px-2">
                {searchQuery ? 'No memories found for your search.' : 'No memories yet. Start a conversation to create some!'}
              </p>
            </div>
          ) : (
            memories.map((memory) => (
              <Card key={memory.id} className="group hover:shadow-sm transition-all hover:border-primary/20">
                <CardContent className="p-3">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(memory.created_at)}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => deleteMemory(memory.id)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity h-5 w-5 hover:bg-destructive/10"
                    >
                      <Trash2 className="h-3 w-3 text-destructive" />
                    </Button>
                  </div>

                  <p className="text-sm leading-relaxed text-foreground/90 line-clamp-4">
                    {memory.memory}
                  </p>

                  {memory.metadata && Object.keys(memory.metadata).length > 0 && (
                    <div className="mt-2">
                      <details className="cursor-pointer group/details">
                        <summary className="text-xs text-muted-foreground hover:text-foreground transition-colors">
                          View metadata
                        </summary>
                        <pre className="mt-1 text-xs bg-muted/50 p-2 rounded text-muted-foreground overflow-auto max-h-20">
                          {JSON.stringify(memory.metadata, null, 2)}
                        </pre>
                      </details>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
