import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Search, Trash2, RefreshCw, Brain, Calendar } from 'lucide-react';
import { memoryApi } from '@/lib/api';
import { Memory } from '@/types/memory';

interface MemoryListProps {
  userId: string;
  refreshTrigger: number;
}

export function MemoryList({ userId, refreshTrigger }: MemoryListProps) {
  const [memories, setMemories] = useState<Memory[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  const loadMemories = async () => {
    setIsLoading(true);
    try {
      const response = await memoryApi.getAllMemories(userId);
      setMemories(response.memories || []);
    } catch (error) {
      console.error('Error loading memories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const searchMemories = async () => {
    if (!searchQuery.trim()) {
      loadMemories();
      return;
    }

    setIsSearching(true);
    try {
      const response = await memoryApi.searchMemories({
        query: searchQuery,
        user_id: userId,
        limit: 20
      });
      setMemories(response.memories || []);
    } catch (error) {
      console.error('Error searching memories:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const deleteMemory = async (memoryId: string) => {
    try {
      await memoryApi.deleteMemory(memoryId);
      setMemories(prev => prev.filter(m => m.id !== memoryId));
    } catch (error) {
      console.error('Error deleting memory:', error);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return 'Unknown date';
    }
  };

  useEffect(() => {
    loadMemories();
  }, [userId, refreshTrigger]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchMemories();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center gap-2 mb-4">
          <Brain className="h-5 w-5 text-primary" />
          <h2 className="text-lg font-semibold">Memory Bank</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={loadMemories}
            disabled={isLoading}
            className="ml-auto"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search memories..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
          {isSearching && (
            <RefreshCw className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground" />
          )}
        </div>
      </div>

      {/* Memory List */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-3">
          {isLoading && memories.length === 0 ? (
            <div className="text-center py-8">
              <RefreshCw className="mx-auto h-8 w-8 animate-spin text-muted-foreground mb-4" />
              <p className="text-muted-foreground">Loading memories...</p>
            </div>
          ) : memories.length === 0 ? (
            <div className="text-center py-8">
              <Brain className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />
              <p className="text-muted-foreground">
                {searchQuery ? 'No memories found for your search.' : 'No memories yet. Start a conversation to create some!'}
              </p>
            </div>
          ) : (
            memories.map((memory) => (
              <Card key={memory.id} className="group hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(memory.created_at)}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => deleteMemory(memory.id)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6"
                    >
                      <Trash2 className="h-3 w-3 text-destructive" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-sm leading-relaxed">{memory.memory}</p>
                  {memory.metadata && Object.keys(memory.metadata).length > 0 && (
                    <div className="mt-2 text-xs text-muted-foreground">
                      <details className="cursor-pointer">
                        <summary>Metadata</summary>
                        <pre className="mt-1 text-xs bg-muted p-2 rounded overflow-auto">
                          {JSON.stringify(memory.metadata, null, 2)}
                        </pre>
                      </details>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t bg-muted/30">
        <p className="text-xs text-muted-foreground text-center">
          {memories.length} {memories.length === 1 ? 'memory' : 'memories'} stored
        </p>
      </div>
    </div>
  );
}
