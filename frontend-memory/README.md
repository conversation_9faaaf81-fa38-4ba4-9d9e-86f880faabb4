# Memory Agent Frontend

A simple and elegant frontend for the Memory Agent service, built with React, shadcn/ui, and TypeScript.

## Features

- **ChatGPT-style Interface**: Clean and intuitive chat interface for interacting with the memory system
- **Memory Management**: View, search, and manage stored memories
- **Real-time Updates**: Automatic refresh of memory list when new memories are added
- **Responsive Design**: Works well on desktop and mobile devices
- **Dark/Light Mode Support**: Built-in theme support with shadcn/ui

## Getting Started

### Prerequisites

- Node.js 18+
- pnpm (preferred) or npm
- Memory Agent service running on port 8010 (Docker)

### Installation

1. Navigate to the frontend directory:
```bash
cd frontend-memory
```

2. Install dependencies:
```bash
pnpm install
```

3. Start the development server:
```bash
pnpm dev
```

4. Open your browser and navigate to `http://localhost:3001`

### Configuration

The frontend is configured to proxy API requests to the Memory Agent service running on `localhost:8010` (Docker). If your service is running on a different port, update the proxy configuration in `vite.config.ts`:

```typescript
server: {
  port: 3001,
  proxy: {
    '/api': {
      target: 'http://localhost:8010', // Change this if needed
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '')
    }
  }
}
```

## Usage

### First Time Setup

1. When you first open the application, you'll be prompted to enter a User ID
2. This ID will be used to associate memories with your account
3. The ID is stored in localStorage for future sessions

### Chat Interface

- Type your message in the text area at the bottom
- Press `Ctrl+Enter` or click the send button to submit
- Your message will be stored as a memory in the system
- The assistant will confirm that your memory has been saved

### Memory Management

- Click the "Memories" tab to view all your stored memories
- Use the search box to find specific memories
- Click the trash icon to delete individual memories
- Memories are displayed with timestamps and metadata

### Keyboard Shortcuts

- `Ctrl+Enter`: Send message in chat interface

## API Integration

The frontend communicates with the Memory Agent service through the following endpoints:

- `GET /`: Service status and health check
- `GET /memories/{user_id}`: Get all memories for a user
- `POST /memories`: Add a new memory
- `POST /memories/search`: Search memories
- `DELETE /memories/{memory_id}`: Delete a specific memory

## Development

### Project Structure

```
src/
├── components/
│   ├── ui/           # shadcn/ui components
│   ├── ChatInterface.tsx
│   └── MemoryList.tsx
├── lib/
│   ├── api.ts        # API client
│   └── utils.ts      # Utility functions
├── types/
│   └── memory.ts     # TypeScript type definitions
├── App.tsx           # Main application component
└── main.tsx          # Application entry point
```

### Building for Production

```bash
pnpm build
```

The built files will be in the `dist` directory.

### Linting

```bash
pnpm lint
```

## Customization

The UI is built with shadcn/ui components and Tailwind CSS, making it easy to customize:

- Modify `tailwind.config.js` for theme customization
- Update CSS variables in `src/index.css` for color scheme changes
- Components in `src/components/ui/` can be modified or replaced

## Troubleshooting

### Service Connection Issues

If you see connection errors:

1. Ensure the Memory Agent service is running on port 8010 (Docker)
2. Check the proxy configuration in `vite.config.ts`
3. Verify CORS settings in the backend service
4. Make sure Docker containers are running: `docker ps | grep agent`

### Memory Not Saving

If memories aren't being saved:

1. Check the browser console for API errors
2. Verify the User ID is set correctly
3. Ensure the backend service has proper database connectivity

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is part of the 3stooges-portal-v2 system.
